<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <com.ondemandbay.huguo.component.MyFontTextView
        android:id="@+id/tvRateVehicleTypeName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_pickup"
        android:gravity="center"
        android:text="Vehicle Type"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@color/white"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/color_swipe"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/activity_margin_register"
        android:orientation="vertical"
        android:padding="@dimen/activity_margin_mainscreen">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/text_base_price_colon"
                android:textAppearance="?android:attr/textAppearanceMedium"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvRateBasePrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textAppearance="?android:attr/textAppearanceMedium"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/text_distance_cost_colon"
                android:textAppearance="?android:attr/textAppearanceMedium"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvRateDistanceCost"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textAppearance="?android:attr/textAppearanceMedium"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/text_time_cost_colon"
                android:textAppearance="?android:attr/textAppearanceMedium"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvRateTimeCost"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textAppearance="?android:attr/textAppearanceMedium"/>
        </LinearLayout>
    </LinearLayout>

    <com.ondemandbay.huguo.component.MyFontTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/activity_margin_mainscreen"
        android:layout_marginRight="@dimen/activity_margin_mainscreen"
        android:gravity="center"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:text="@string/text_airport_rate"
        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="@color/color_app_gray"/>

</LinearLayout>