<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@color/white"
              android:orientation="vertical">

    <com.ondemandbay.huguo.component.MyFontTextView
        android:id="@+id/tvDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/section_box_history"
        android:gravity="center_vertical"
        android:paddingLeft="@dimen/activity_margin_mainscreen"
        android:text="Medium Text"
        android:textAllCaps="true"
        android:textColor="@color/color_text_dark"
        android:textSize="@dimen/size_label"/>

</LinearLayout>