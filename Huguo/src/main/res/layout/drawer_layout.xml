<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout android:id="@+id/drawer_layout"
                                        xmlns:android="http://schemas.android.com/apk/res/android"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent">

    <!-- The main content view -->

    <LinearLayout
        android:id="@+id/llContent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <FrameLayout
            android:id="@+id/content_frame"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </LinearLayout>

    <!-- The navigation drawer -->
    <ListView
        android:id="@+id/left_drawer"
        android:layout_width="200dp"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@drawable/bg_menu"
        android:choiceMode="singleChoice"
        android:divider="@drawable/nav_divider_line"
        android:fitsSystemWindows="true"
        android:footerDividersEnabled="false"
        android:headerDividersEnabled="false"
        android:overScrollFooter="@android:color/transparent"
        android:overScrollHeader="@android:color/transparent"/>

</androidx.drawerlayout.widget.DrawerLayout>