<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/white_trans_light"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/activity_margin_register"
        android:layout_weight="0.5"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/cancelVehicleDetail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="right"
            android:layout_marginRight="@dimen/activity_margin_register"
            android:padding="@dimen/activity_margin_register"
            android:src="@drawable/close_payment"/>

        <com.ondemandbay.huguo.component.MyFontTextView
            android:id="@+id/tvRateVehicleTypeName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/dimen_fp_margin"
            android:layout_marginTop="@dimen/dimen_fp_margin"
            android:gravity="center"
            android:text="Sedan"
            android:textColor="@color/black"
            android:textSize="@dimen/size_dialog_big"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="@string/text_min_fare"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>

            <com.ondemandbay.huguo.component.MyFontTextViewMedium
                android:id="@+id/tvRateBasePrice"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="@string/text_per_km"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>

            <com.ondemandbay.huguo.component.MyFontTextViewMedium
                android:id="@+id/tvRateDistanceCost"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="@string/text_time_cost_colon"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>

            <com.ondemandbay.huguo.component.MyFontTextViewMedium
                android:id="@+id/tvRateTimeCost"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="$50"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:orientation="horizontal">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="right"
                android:text="@string/text_max_size"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>

            <com.ondemandbay.huguo.component.MyFontTextViewMedium
                android:id="@+id/tvMaxSize"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="@dimen/dimen_history_one"
                android:text="5 Person"
                android:textColor="@color/black"
                android:textSize="@dimen/size_general"/>
        </LinearLayout>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@drawable/divider_register"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.5"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/feedback_margin_three"
                android:layout_marginTop="@dimen/feedback_margin_three"
                android:layout_weight="1"
                android:orientation="vertical">

                <com.ondemandbay.huguo.component.MyFontTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/text_eta"
                    android:textColor="@color/black"
                    android:textSize="@dimen/size_general"/>

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.ondemandbay.huguo.component.MyFontTextViewMedium
                        android:id="@+id/tvETA"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/feedback_padding"
                        android:gravity="center"
                        android:text="3 mins"
                        android:textColor="@color/black"
                        android:textSize="@dimen/size_eta_minfare"
                        android:visibility="visible"/>

                    <ProgressBar
                        android:id="@+id/pbar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/feedback_padding"/>
                </RelativeLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginBottom="@dimen/feedback_margin_three"
                android:layout_marginTop="@dimen/feedback_margin_three"
                android:layout_weight="1"
                android:orientation="vertical">

                <com.ondemandbay.huguo.component.MyFontTextView
                    android:id="@+id/tvLblMinFare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/text_min_fare_caps"
                    android:textColor="@color/black"
                    android:textSize="@dimen/size_general"/>

                <ProgressBar
                    android:id="@+id/pbMinFare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/feedback_padding"
                    android:gravity="center"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:id="@+id/tvMinFare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/feedback_padding"
                    android:gravity="center"
                    android:text="$30"
                    android:textColor="@color/black"
                    android:textSize="@dimen/size_eta_minfare"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:id="@+id/tvTotalFare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="R30"
                    android:textColor="@color/black"
                    android:textSize="@dimen/size_eta_minfare"
                    android:visibility="gone"/>
            </LinearLayout>
        </LinearLayout>

        <com.ondemandbay.huguo.component.MyFontTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_margin_register"
            android:gravity="center"
            android:padding="@dimen/activity_margin_register"
            android:text="@string/text_estimate_fare"
            android:textColor="@color/black"
            android:textSize="@dimen/size_general_small"/>
    </LinearLayout>

    <com.ondemandbay.huguo.component.MyFontTextView
        android:id="@+id/tvGetFareEst"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_pickup"
        android:gravity="center"
        android:maxLines="2"
        android:text="@string/text_get_fare_estimate"
        android:textColor="@color/white"
        android:textSize="@dimen/size_general"/>

    <!-- <com.automated.taxinow.component.MyFontTextView -->
    <!-- android:id="@+id/tvRateCard" -->
    <!-- android:layout_width="0dp" -->
    <!-- android:layout_height="match_parent" -->
    <!-- android:layout_marginLeft="1dp" -->
    <!-- android:layout_weight="1" -->
    <!-- android:background="@drawable/bg_btn_small" -->
    <!-- android:gravity="center" -->
    <!-- android:padding="@dimen/activity_margin_register" -->
    <!-- android:text="@string/text_rate_card" -->
    <!-- android:textColor="@color/white" /> -->
    <!-- </LinearLayout> -->

</LinearLayout>