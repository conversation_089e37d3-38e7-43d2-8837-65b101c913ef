<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@drawable/bg_splash"
              android:gravity="center_horizontal"
              android:orientation="vertical"
              tools:context=".MainDrawerActivity">

 <ImageView
            android:id="@+id/ivAppName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:background="@null"
     android:src="@drawable/logotemp"
     android:scaleType="fitCenter"/>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="250dp"
        android:layout_weight="1">

        <ImageView
            android:id="@+id/booknowTaxi"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:background="@null"
            android:visibility="gone"
            android:src="@drawable/booknow" />
    </LinearLayout>




        <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <com.ondemandbay.huguo.component.MyFontButton
            android:id="@+id/btnSignIn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginRight="1dp"
            android:background="@drawable/bg_btn_small_light"
            android:gravity="center"
            android:text="@string/text_signin"
            android:textColor="@color/white"
            android:textSize="@dimen/size_general"/>

        <com.ondemandbay.huguo.component.MyFontButton
            android:id="@+id/btnRegister"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginLeft="1dp"
            android:background="@drawable/bg_btn_small"
            android:gravity="center"
            android:text="@string/text_register"
            android:textColor="@color/white"
            android:textSize="@dimen/size_general" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
    <com.ondemandbay.huguo.component.MyFontTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingBottom="@dimen/dimen_fp_margin"
        android:paddingTop="@dimen/dimen_fp_margin"
        android:text="@string/text_copyright"
        android:layout_weight="1"
        android:gravity="center_horizontal"

        android:textAppearance="?android:attr/textAppearanceSmall"
        android:textColor="@color/white"/>

    <TextView
        android:id="@+id/textViewPrivacyPolicy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="@string/privacy_policy"
        android:textColor="@color/wallet_holo_blue_light"
        android:gravity="center_horizontal"

        />
    </LinearLayout>

</LinearLayout>