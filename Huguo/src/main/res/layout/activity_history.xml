<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:gravity="center"
              android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/feedback_padding"
        android:background="@drawable/sarch_box"
        android:padding="@dimen/dimen_fp_margin">

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/white"
            android:orientation="vertical">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/dateFrom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/text_from"
                android:textSize="15.5sp"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/fromDateBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/dateFrom"
                android:drawableEnd="@drawable/date"
                android:drawablePadding="3dp"
                android:drawableRight="@drawable/date"
                android:gravity="center"
                android:text="@string/text_select_date"
                android:textColor="@color/theme_color"
                android:textSize="15.5sp"/>
        </RelativeLayout>

        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@color/white"
            android:orientation="vertical">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/dateTo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/text_to"
                android:textSize="15.5sp"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/toDateBtn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/dateTo"
                android:drawableEnd="@drawable/date"
                android:drawablePadding="3dp"
                android:drawableRight="@drawable/date"
                android:gravity="center"
                android:text="@string/text_select_date"
                android:textColor="@color/theme_color"
                android:textSize="15.5sp"/>
        </RelativeLayout>

        <ImageButton
            android:id="@+id/btnSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:background="@null"
            android:padding="@dimen/feedback_padding"
            android:src="@drawable/sarch"
            android:text="Search"/>
    </LinearLayout>

    <com.hb.views.PinnedSectionListView
        android:id="@+id/lvHistory"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white"
        android:divider="@drawable/divider_register"
        android:visibility="gone">
    </com.hb.views.PinnedSectionListView>

    <ImageView
        android:id="@+id/ivEmptyView"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@null"
        android:gravity="center"
        android:src="@drawable/no_items"/>

</LinearLayout>