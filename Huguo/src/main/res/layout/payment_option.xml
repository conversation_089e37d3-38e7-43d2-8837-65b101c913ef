<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:background="@drawable/ellipse_contacting"
              android:gravity="center"
              android:orientation="vertical">

    <com.ondemandbay.huguo.component.MyFontTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/text_schedule_trip"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:textColor="@color/white"
        android:textSize="@dimen/size_dialog_big"/>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/activity_horizontal_margin"
        android:orientation="horizontal"
        android:weightSum="2">

        <!--         <LinearLayout -->
        <!--             android:id="@+id/llCash" -->
        <!--             android:layout_width="wrap_content" -->
        <!--             android:layout_height="match_parent" -->
        <!--             android:layout_marginRight="@dimen/dimen_fp_margin_top" -->
        <!--             android:gravity="center" -->
        <!--             android:orientation="vertical" > -->

        <!--             <ImageView -->
        <!--                 android:layout_width="wrap_content" -->
        <!--                 android:layout_height="0dp" -->
        <!--                 android:layout_weight="1" -->
        <!--                 android:src="@drawable/cash_selector" /> -->

        <com.ondemandbay.huguo.component.MyFontTextView
            android:id="@+id/btnRideNow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_fp_margin"
            android:layout_weight="1"
            android:text="@string/text_ride_now"
            android:textColor="@color/white"
            android:textSize="@dimen/size_general"/>

        <!--         </LinearLayout> -->

        <!--         <LinearLayout -->
        <!--             android:id="@+id/llCard" -->
        <!--             android:layout_width="wrap_content" -->
        <!--             android:layout_height="match_parent" -->
        <!--             android:orientation="vertical" > -->

        <!--             <ImageView -->
        <!--                 android:layout_width="wrap_content" -->
        <!--                 android:layout_height="0dp" -->
        <!--                 android:layout_gravity="center" -->
        <!--                 android:layout_weight="1" -->
        <!--                 android:src="@drawable/card_selector" /> -->

        <com.ondemandbay.huguo.component.MyFontTextView
            android:id="@+id/btnRideLater"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="@dimen/dimen_fp_margin"
            android:layout_weight="1"
            android:text="@string/text_ride_later"
            android:textColor="@color/white"
            android:textSize="@dimen/size_general"/>
        <!--         </LinearLayout> -->

    </LinearLayout>

</LinearLayout>