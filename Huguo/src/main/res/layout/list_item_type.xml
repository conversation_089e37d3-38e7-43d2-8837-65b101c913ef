<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/buttonLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="5dp">

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="5dp"/>

        <ImageView
            android:id="@+id/ivSelectService"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@null"
            android:src="@drawable/selected_service"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/tvType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@null"
        android:gravity="center"
        android:textAllCaps="true"
        android:textColor="@color/color_app_gray"
        android:textSize="@dimen/ub_textsize_search_small"
        android:textStyle="bold"/>

</LinearLayout>