<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/progressLayout"
              xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="fill_parent"
              android:layout_height="fill_parent"
              android:layout_centerInParent="true"
              android:background="@drawable/bg_loding"
              android:gravity="center"
              android:orientation="vertical"
              android:visibility="visible">

    <include
        android:id="@+id/includeDriver"
        layout="@layout/include_detail_view"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:paddingLeft="@dimen/activity_horizontal_margin"
        android:paddingRight="@dimen/activity_horizontal_margin"
        android:paddingTop="@dimen/activity_vertical_margin">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:src="@drawable/loading_car"/>

            <ImageView
                android:id="@+id/ivProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/loading"/>
        </RelativeLayout>

        <!-- <ImageView -->
        <!-- android:id="@+id/ivProgressBar" -->
        <!-- android:layout_width="wrap_content" -->
        <!-- android:layout_height="wrap_content" -->
        <!-- android:background="@drawable/loading" -->
        <!-- android:visibility="visible" > -->
        <!-- </ImageView> -->

        <com.ondemandbay.huguo.component.MyTitleFontTextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:gravity="center"
            android:text="@string/text_waiting_for_confirm"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:textColor="@color/white"
            android:textSize="@dimen/size_dialog_small"/>
    </LinearLayout>

    <com.ondemandbay.huguo.component.MyFontButton
        android:id="@+id/btnCancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_pickup"
        android:gravity="center"
        android:text="@string/text_cancel"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@color/white"/>

</LinearLayout>