<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.gms.maps.MapView
            android:id="@+id/maptrip"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

        <RelativeLayout
            android:id="@+id/flDriverDetail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/white_trans">

            <ImageView
                android:id="@+id/lvNotificationLine"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/popup_margin_left"
                android:layout_marginTop="@dimen/popup_margin_top"
                android:background="@null"
                android:src="@drawable/notification_line"
                android:visibility="gone"/>

            <RelativeLayout
                android:id="@+id/rlPopupWindow"
                xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/btnCancelTrip"
                android:layout_below="@+id/includeDriverDetail"
                android:orientation="vertical"
                android:paddingRight="@dimen/popup_margin_text"
                android:paddingTop="@dimen/popup_margin_top_between_notification"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivJobAccepted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/popup_margin_left_image"
                            android:background="@null"
                            android:src="@drawable/un_checkbox"/>

                        <com.ondemandbay.huguo.component.MyFontTextView
                            android:id="@+id/tvJobAccepted"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="@dimen/popup_margin_text"
                            android:layout_marginRight="@dimen/popup_margin_text"
                            android:text="@string/text_job_accepted"
                            android:textColor="@color/color_text_popup"
                            android:textSize="@dimen/font_size_popup_big"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/popup_margin_top_between_notification"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <ImageView
                            android:id="@+id/ivDriverStarted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/popup_margin_left_image"
                            android:background="@null"
                            android:src="@drawable/un_checkbox"/>

                        <com.ondemandbay.huguo.component.MyFontTextView
                            android:id="@+id/tvDriverStarted"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="@dimen/popup_margin_text"
                            android:layout_marginRight="@dimen/popup_margin_text"
                            android:text="@string/text_driver_started"
                            android:textColor="@color/color_text_popup"
                            android:textSize="@dimen/font_size_popup_big"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/popup_margin_top_between_notification"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivDriverArrvied"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/popup_margin_left_image"
                            android:background="@null"
                            android:src="@drawable/un_checkbox"/>

                        <com.ondemandbay.huguo.component.MyFontTextView
                            android:id="@+id/tvDriverArrvied"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="@dimen/popup_margin_text"
                            android:layout_marginRight="@dimen/popup_margin_text"
                            android:text="@string/text_driver_arrvied"
                            android:textColor="@color/color_text_popup"
                            android:textSize="@dimen/font_size_popup_big"/>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/popup_margin_top_between_notification"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivTripStarted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/popup_margin_left_image"
                            android:background="@null"
                            android:src="@drawable/un_checkbox"/>

                        <com.ondemandbay.huguo.component.MyFontTextView
                            android:id="@+id/tvTripStarted"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="@dimen/popup_margin_text"
                            android:layout_marginRight="@dimen/popup_margin_text"
                            android:text="@string/text_trip_started"
                            android:textColor="@color/color_text_popup"
                            android:textSize="@dimen/font_size_popup_big"/>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llLastNotification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/popup_margin_top_between_notification"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivTripCompleted"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="@dimen/popup_margin_left_image"
                            android:background="@null"
                            android:src="@drawable/un_checkbox"/>

                        <com.ondemandbay.huguo.component.MyFontTextView
                            android:id="@+id/tvTripCompleted"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginLeft="@dimen/popup_margin_text"
                            android:layout_marginRight="@dimen/popup_margin_text"
                            android:text="@string/text_trip_completed"
                            android:textColor="@color/color_text_popup"
                            android:textSize="@dimen/font_size_popup_big"/>
                    </LinearLayout>
                </LinearLayout>
            </RelativeLayout>

            <include
                android:id="@+id/includeDriverDetail"
                layout="@layout/include_detail_view"/>
        </RelativeLayout>

        <ImageView
            android:id="@+id/btnCall"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginTop="@dimen/dimen_margin_call_trip"
            android:src="@drawable/call"/>

        <ImageButton
            android:id="@+id/btnCancelTrip"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:visibility="gone"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:layout_margin="@dimen/feedback_padding"
            android:background="@drawable/cancel_trip"/>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/layoutCardDetails"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal">

        <FrameLayout
            android:id="@+id/layoutCash"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/cash_notification"
            android:minHeight="0dp"
            android:visibility="gone"
            android:minWidth="0dp">

            <TextView
                android:id="@+id/tvCash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/wallet"
                android:drawablePadding="@dimen/activity_margin_register"
                android:text="@string/text_cash"
                android:textColor="@color/white"
                android:textSize="@dimen/size_general"/>

            <ImageView
                android:id="@+id/imgSelectedCash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:src="@drawable/check_notification"
                android:visibility="gone"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/layoutCard"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/card_notification">

            <ImageView
                android:id="@+id/imgSelectedCard"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:src="@drawable/check_notification"
                android:visibility="gone"/>

            <TextView
                android:id="@+id/tvCardNo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@drawable/cash"
                android:drawablePadding="@dimen/activity_margin_register"
                android:text="@string/text_card"
                android:textColor="@color/white"
                android:textSize="@dimen/size_general"/>
        </FrameLayout>

        <com.ondemandbay.huguo.component.MyFontEdittextView
            android:id="@+id/ibApplyPromo"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@null"
            android:gravity="center"
            android:hint="@string/text_enter_promo_code"
            android:padding="2dp"
            android:singleLine="true"
            android:textSize="@dimen/size_general_small"/>
    </LinearLayout>

    <!--     <LinearLayout -->
    <!--         android:layout_width="match_parent" -->
    <!--         android:layout_height="wrap_content" -->
    <!--         android:background="@color/white" -->
    <!--         android:gravity="center" -->
    <!--         android:orientation="horizontal" -->
    <!--         android:visibility="gone" > -->

    <!--         <LinearLayout -->
    <!--             android:layout_width="0dp" -->
    <!--             android:layout_height="wrap_content" -->
    <!--             android:layout_weight="1" -->
    <!--             android:gravity="center" > -->

    <!--             <com.automated.taxinow.component.MyFontTextView -->
    <!--                 android:id="@+id/tvJobTime" -->
    <!--                 android:layout_width="wrap_content" -->
    <!--                 android:layout_height="wrap_content" -->
    <!--                 android:layout_marginRight="@dimen/activity_margin_register" -->
    <!--                 android:drawableLeft="@drawable/clock" -->
    <!--                 android:drawablePadding="10dp" -->
    <!--                 android:gravity="center" -->
    <!--                 android:text="0 mins" -->
    <!--                 android:textAllCaps="true" -->
    <!--                 android:textColor="@color/darkgray" /> -->
    <!--         </LinearLayout> -->

    <!--         <ImageView -->
    <!--             android:layout_width="wrap_content" -->
    <!--             android:layout_height="wrap_content" -->
    <!--             android:layout_gravity="center" -->
    <!--             android:background="@null" -->
    <!--             android:src="@drawable/line_divider" /> -->

    <!--         <LinearLayout -->
    <!--             android:layout_width="0dp" -->
    <!--             android:layout_height="wrap_content" -->
    <!--             android:layout_weight="1" -->
    <!--             android:gravity="center" > -->

    <!--             <com.automated.taxinow.component.MyFontTextView -->
    <!--                 android:id="@+id/tvJobDistance" -->
    <!--                 android:layout_width="wrap_content" -->
    <!--                 android:layout_height="wrap_content" -->
    <!--                 android:layout_marginRight="@dimen/activity_margin_register" -->
    <!--                 android:drawableLeft="@drawable/mile" -->
    <!--                 android:drawablePadding="10dp" -->
    <!--                 android:gravity="center_vertical" -->
    <!--                 android:text="0 kms" -->
    <!--                 android:textAllCaps="true" -->
    <!--                 android:textColor="@color/darkgray" /> -->
    <!--         </LinearLayout> -->

    <!--         <ImageView -->
    <!--             android:layout_width="wrap_content" -->
    <!--             android:layout_height="wrap_content" -->
    <!--             android:layout_gravity="center" -->
    <!--             android:background="@null" -->
    <!--             android:src="@drawable/line_divider" /> -->

    <!--         <LinearLayout -->
    <!--             android:layout_width="0dp" -->
    <!--             android:layout_height="wrap_content" -->
    <!--             android:layout_weight="1" -->
    <!--             android:gravity="center" > -->
    <!--         </LinearLayout> -->
    <!--     </LinearLayout> -->

</LinearLayout>