<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:gravity="center_horizontal"
              android:orientation="vertical">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ivDriverImage"
        android:layout_width="@dimen/dimen_feedback_image_width"
        android:layout_height="@dimen/dimen_feedback_image_height"
        android:layout_centerVertical="true"
        android:layout_marginBottom="@dimen/dimen_fp_margin"
        android:layout_marginTop="@dimen/dimen_fp_margin"
        android:src="@drawable/default_user"
        app:civ_border_color="@color/white"
        app:civ_border_width="2dp"/>

    <com.ondemandbay.huguo.component.MyFontTextView
        android:id="@+id/tvClientName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Samantha Ayrton"
        android:textSize="@dimen/size_name"/>

    <RatingBar
        android:id="@+id/ratingBar"
        style="@style/feedbackRatingBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/dimen_fp_margin"
        android:layout_marginTop="@dimen/dimen_fp_margin"
        android:stepSize="0.5"/>

</LinearLayout>