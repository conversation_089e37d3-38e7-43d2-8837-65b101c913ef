<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:background="@color/white"
              android:orientation="vertical">

    <ScrollView
        android:id="@+id/scrollView1"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llRegisterSocial"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/dimen_fp_margin"
                android:layout_marginTop="@dimen/dimen_fp_margin"
                android:visibility="gone"
                android:orientation="horizontal"
                android:gravity="center">

<!--                <ImageButton-->
<!--                    android:id="@+id/btnFb"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:background="@null"-->
<!--                    android:src="@drawable/signupfb"/>-->

                <ImageButton
                    android:id="@+id/btnGplus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:src="@drawable/signupgoogle"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_profile"
                android:gravity="center"
                android:orientation="horizontal">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/ivChooseProPic"
                    android:layout_width="@dimen/menu_img_width"
                    android:layout_height="@dimen/menu_img_height"
                    android:src="@drawable/default_user"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:visibility="gone"
                    android:orientation="vertical">

                    <com.ondemandbay.huguo.component.MyTitleFontTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/dimen_fp_margin"
                        android:layout_marginRight="@dimen/dimen_fp_margin"
                        android:text="@string/text_upload_picture"
                        android:textAllCaps="true"
                        android:textColor="@color/white"
                        android:textSize="@dimen/size_label"/>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dimen_history_one"
                        android:gravity="center"

                        android:orientation="horizontal">

                        <ImageButton
                            android:id="@+id/btnClickPhoto"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginRight="@dimen/feedback_margin"
                            android:background="@null"
                            android:src="@drawable/photocam_register"/>

                        <ImageButton
                            android:id="@+id/btnPhotoFromGalary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:src="@drawable/picture_register"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- <ScrollView -->
            <!-- android:id="@+id/scrollView1" -->
            <!-- android:layout_width="match_parent" -->
            <!-- android:layout_height="0dp" -->
            <!-- android:layout_weight="1" > -->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingBottom="@dimen/activity_vertical_margin"
                android:paddingTop="@dimen/activity_vertical_margin">

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_fname_caps"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etFName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_fname_hint"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"/>


                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_lname_caps"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etLName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_lname_hint"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"/>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_email"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <!-- <LinearLayout -->
                <!-- android:layout_width="match_parent" -->
                <!-- android:layout_height="wrap_content" -->
                <!-- android:layout_marginTop="@dimen/activity_vertical_margin" -->
                <!-- android:background="@drawable/bg_edittext" -->
                <!-- android:orientation="horizontal" > -->

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_email_hint"
                    android:inputType="textEmailAddress"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"/>

                <!-- <ImageView -->
                <!-- android:id="@+id/btnRegisterEmailInfo" -->
                <!-- android:layout_width="wrap_content" -->
                <!-- android:layout_height="wrap_content" -->
                <!-- android:layout_gravity="center_vertical" -->
                <!-- android:background="@null" -->
                <!-- android:src="@drawable/info" /> -->
                <!-- </LinearLayout> -->

                <ImageView
                    android:id="@+id/ivPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:id="@+id/tvPassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_passs"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_pass_hint"
                    android:inputType="textPassword"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"/>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_number"
                    android:textAllCaps="true"
                    android:visibility="visible"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/activity_vertical_margin"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:visibility="visible"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:orientation="horizontal">

                    <com.ondemandbay.huguo.component.MyFontTextViewMedium
                        android:id="@+id/spCCode"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginRight="@dimen/activity_margin_register"
                        android:layout_weight="0.3"
                        android:background="@drawable/spinner_ab_default_holo_light"
                        android:gravity="center"
                        android:singleLine="true"
                        android:textColor="@color/color_text_dark"
                        android:textSize="@dimen/size_label"/>

                    <com.ondemandbay.huguo.component.MyFontEdittextView
                        android:id="@+id/etNumber"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_edittext"
                        android:ems="10"
                        android:hint="@string/text_number_hint"
                        android:inputType="numberSigned"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:singleLine="true"
                        android:textColor="@color/color_text_dark"
                        android:maxLength="10"
                        android:textSize="@dimen/size_general"/>
                    <com.ondemandbay.huguo.component.MyFontButton
                        android:id="@+id/btnSend"
                        android:layout_width="60dp"
                        android:layout_height="30dp"
                        android:background="@drawable/bg_btn_small_light"
                        android:padding="3dp"
                        android:visibility="gone"
                        android:layout_marginLeft="8dp"
                        android:gravity="center"
                        android:text="Envoyer"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        android:textSize="12sp"/>
                </LinearLayout>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"
                    android:visibility="visible"/>
                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="Vérification code"
                    android:visibility="gone"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"/>

                <!-- <LinearLayout -->
                <!-- android:layout_width="match_parent" -->
                <!-- android:layout_height="wrap_content" -->
                <!-- android:layout_marginTop="@dimen/activity_vertical_margin" -->
                <!-- android:background="@drawable/bg_edittext" -->
                <!-- android:orientation="horizontal" > -->

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etVerifyCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:visibility="gone"
                    android:ems="10"
                    android:hint="Entrez le code de vérification du téléphone."
                    android:inputType="numberDecimal"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_address"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etAddress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_address"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"
                    android:visibility="gone"/>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_bio"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text_label"
                    android:textSize="@dimen/size_label"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etBio"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_bio"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"
                    android:visibility="gone"/>

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/dimen_fp_margin"
                    android:layout_marginTop="@dimen/dimen_fp_margin"
                    android:src="@drawable/divider_register"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontTextViewMedium
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:text="@string/text_zip"
                    android:textAllCaps="true"
                    android:textColor="@color/color_text"
                    android:textSize="@dimen/size_label"
                    android:visibility="gone"/>

                <com.ondemandbay.huguo.component.MyFontEdittextView
                    android:id="@+id/etZipCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/activity_vertical_margin"
                    android:layout_marginLeft="@dimen/activity_horizontal_margin"
                    android:layout_marginRight="@dimen/activity_horizontal_margin"
                    android:background="@null"
                    android:ems="10"
                    android:hint="@string/text_zip"
                    android:inputType="number"
                    android:singleLine="true"
                    android:textColor="@color/color_text_dark"
                    android:textSize="@dimen/size_general"
                    android:visibility="gone"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <com.ondemandbay.huguo.component.MyFontButton
        android:id="@+id/btnNext"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/btn_done"/>

</LinearLayout>