<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical"
              android:paddingBottom="@dimen/activity_vertical_margin"
              android:paddingLeft="@dimen/activity_horizontal_margin"
              android:paddingRight="@dimen/activity_horizontal_margin"
              android:paddingTop="@dimen/activity_vertical_margin">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@drawable/round_rect"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/imageView1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:src="@drawable/ic_launcher">
        </ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/tvRate"
            android:orientation="vertical"
            android:padding="@dimen/activity_margin_register">

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:textAppearance="?android:attr/textAppearanceMedium"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvDetail"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="TextView"
                android:textAppearance="?android:attr/textAppearanceMedium"/>
        </LinearLayout>

        <com.ondemandbay.huguo.component.MyFontTextView
            android:id="@+id/tvRate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:drawableLeft="@drawable/star_feedback"
            android:drawablePadding="5dp"
            android:gravity="center"
            android:padding="@dimen/activity_margin_register"
            android:text="TextView"
            android:textAppearance="?android:attr/textAppearanceMedium"/>
    </RelativeLayout>

</LinearLayout>