<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:descendantFocusability="blocksDescendants"
              android:gravity="center_vertical"
              android:orientation="horizontal"
              android:padding="16.0dip">

    <!-- <ImageButton -->
    <!-- android:id="@+id/ivMenuImage" -->
    <!-- android:layout_width="@dimen/menu_item_size" -->
    <!-- android:layout_height="@dimen/menu_item_size" -->
    <!-- android:background="@null" -->
    <!-- android:scaleType="centerInside" -->
    <!-- android:src="@android:drawable/alert_dark_frame" /> -->

    <com.ondemandbay.huguo.component.MyFontTextView
        android:id="@+id/tvMenuItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Profile"
        android:textColor="@color/white"
        android:textSize="@dimen/size_general"/>

</LinearLayout>