<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/ivVehicle"
        android:layout_width="@dimen/image_type_width"
        android:layout_height="@dimen/image_type_height_width"
        android:src="@drawable/ic_launcher"
        android:padding="6dp"
        android:scaleType="fitXY"
        app:riv_corner_radius="30dip"
        app:riv_border_width="1dip"
        app:riv_border_color="#333333"
        app:riv_mutate_background="true"
        app:riv_oval="true"/>

</LinearLayout>