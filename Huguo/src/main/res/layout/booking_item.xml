<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:descendantFocusability="blocksDescendants"
              android:orientation="vertical">

    <LinearLayout
        android:id="@+id/tripStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp">

        <com.ondemandbay.huguo.component.MyFontTextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:text="@string/text_status"
            android:textColor="@color/color_text_dark"
            android:textSize="@dimen/size_general"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/calendarImg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvDateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginStart="10dp"
                android:text="Today at 10:10 PM"
                android:textColor="@color/color_text_light"
                android:textSize="@dimen/size_general_small"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginStart="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/sourceImg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <com.ondemandbay.huguo.component.MyFontTextView
                android:id="@+id/tvSourceAddr"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/tvDateTime"
                android:layout_marginLeft="10dp"
                android:layout_marginStart="10dp"
                android:text="Address"
                android:textColor="@color/color_text_light"
                android:textSize="@dimen/size_general_small"/>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>