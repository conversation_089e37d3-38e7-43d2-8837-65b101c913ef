<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:id="@+id/picker_layout"
              xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:layout_gravity="center"
              android:orientation="vertical"
              android:paddingTop="@dimen/activity_margin_register">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:text="Select Date"
            android:textSize="20sp"
            android:textStyle="bold"/>

        <DatePicker
            android:id="@+id/date_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:datePickerMode="spinner"
            android:calendarViewShown="false">
        </DatePicker>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textSize="20sp"
            android:textStyle="bold"
            android:text="Select Time"/>
        <TimePicker
            android:id="@+id/time_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:timePickerMode="spinner"
            android:layout_marginTop="5dp">
        </TimePicker>
    </LinearLayout>

    <com.ondemandbay.huguo.component.MyFontButton
        android:id="@+id/confirm_schedule"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/bg_btn_small"
        android:text="@string/text_select_date"
        android:textColor="@color/white"/>

</LinearLayout>