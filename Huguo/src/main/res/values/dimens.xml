<resources>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="activity_margin_register">5dp</dimen>
    <dimen name="activity_margin_mainscreen">30dp</dimen>
    <dimen name="activity_margin_feedback">20dp</dimen>
    <dimen name="font_small">11sp</dimen>
    <dimen name="rating_min_height">19dip</dimen>
    <dimen name="rating_max_height">19dip</dimen>
    <dimen name="image_type_width">89dp</dimen>
    <dimen name="image_type_height_width">50dp</dimen>
    <dimen name="drawer_font_size">15.0sp</dimen>
    <dimen name="ub_view_margin">10.0dip</dimen>
    <dimen name="ub_search_padding_medium">16.0dip</dimen>
    <dimen name="ub_textsize_search_small">12.0sp</dimen>
    <dimen name="ub_textsize_search_big">15.0sp</dimen>
    <dimen name="ub_textsize_extra_big">45.0sp</dimen>
    <dimen name="ub_textsize_big">35.0sp</dimen>
    <dimen name="ub_textsize_medium">18.0sp</dimen>
    <dimen name="ub_textsize_small">13.0sp</dimen>
    <dimen name="margin_left_right_bill">20dip</dimen>
    <dimen name="ub_textsize_huge">60.0sp</dimen>
    <dimen name="dimen_address_padding">8dp</dimen>
    <dimen name="dimen_address_textsize">18sp</dimen>
    <dimen name="dimen_history_drawble_padding">5dp</dimen>
    <dimen name="dimen_hostory_bottom_textsize">15sp</dimen>
    <dimen name="dimen_button_textsize">22sp</dimen>
    <dimen name="dimen_margin_my_location">70dp</dimen>
    <dimen name="dimen_vehicle_margin">60dp</dimen>

</resources>