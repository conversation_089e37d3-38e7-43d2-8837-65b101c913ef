<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:versionCode="9"
    android:versionName="3.2"
    xmlns:tools="http://schemas.android.com/tools">



    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.CALL_PHONE"/>
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
    <uses-permission android:name="android.permission.CAMERA"/>
    <uses-permission android:name="android.permission.USE_CREDENTIALS"/>
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES"/>
    <uses-permission android:name="android.permission.CALL_PRIVILEGED"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_LOGS"/>
    <uses-permission android:name="android.permission.WRITE_INTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>



    <permission
        android:name="com.ondemandbay.huguo.permission.C2D_MESSAGE"
        android:protectionLevel="signature"/>

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true"/>
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"/>
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false"/>

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:theme="@style/AppTheme">

        <uses-library android:name="org.apache.http.legacy" android:required="false" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version"/>
        <meta-data
            android:name="com.google.android.maps.v2.API_KEY"
            android:value="@string/map_api_key"/>

        <activity android:name="com.ondemandbay.huguo.PrivacyPolicyActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:theme="@style/AppTheme.NoActionBar"

            />

        <activity
            android:name="com.ondemandbay.huguo.RegisterActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan">
        </activity>
        <activity android:name="com.soundcloud.android.crop.CropImageActivity"/>
        <activity
            android:name="com.ondemandbay.huguo.MainDrawerActivity"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan">
        </activity>
        <activity tools:replace="android:theme"
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:label="@string/app_name" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/applicationId"/>
        <!--<receiver
            android:name="com.google.android.gcm.GCMBroadcastReceiver"
            android:permission="com.google.android.c2dm.permission.SEND">
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE"/>
                <action android:name="com.google.android.c2dm.intent.REGISTRATION"/>

                <category android:name="com.ondemandbay.taxianytime"/>
            </intent-filter>
        </receiver>-->

        <!--<service android:name="GCMIntentService"/>-->

        <activity
            android:name="com.ondemandbay.huguo.AddPaymentActivity"
            android:screenOrientation="portrait">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.ProfileActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan|stateHidden">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.BookingDetailsActivity"
            android:screenOrientation="portrait">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.MenuDescActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.ViewPaymentActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.HistoryActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
        </activity>
        <activity
            android:name="com.ondemandbay.huguo.HistoryDetailsActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden">
        </activity>

        <activity
            android:name="com.ondemandbay.huguo.MainActivity"
            android:screenOrientation="portrait"
            android:exported="true"
            android:theme="@android:style/Theme.Light.NoTitleBar">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER"/>

                <action android:name="android.intent.action.MAIN"/>
            </intent-filter>
        </activity>

        <!--firebase-->
        <service
            android:exported="true"

            android:name=".firebase.MyFirebaseMessagingService">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT"/>
            </intent-filter>
        </service>

        <service
            android:exported="true"

            android:name=".firebase.MyFirebaseInstanceIDService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT"/>
            </intent-filter>
        </service>
        <!--<service
            android:name=".MyFcmListenerService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT"/>
            </intent-filter>
        </service>


        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/logo" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/bill_back_color" />-->


        <meta-data
            android:name="io.fabric.ApiKey"
            android:value="6adbd2683865cdc979860fd7c49920d89bcaf4b6" />
    </application>

</manifest>