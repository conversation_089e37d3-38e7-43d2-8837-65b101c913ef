package com.ondemandbay.huguo;

import android.app.Service;
import android.util.Log;
import android.widget.Toast;

import com.google.firebase.iid.FirebaseInstanceId;
import com.google.firebase.iid.FirebaseInstanceIdService;
import com.ondemandbay.huguo.utils.AndyUtils;
import com.ondemandbay.huguo.utils.PreferenceHelper;

/**
 * Created by Abid on 5/2/2018.
 */

public class MyFcmListenerService extends FirebaseInstanceIdService {
    @Override
    public void onTokenRefresh() {
        String refreshedToken = FirebaseInstanceId.getInstance().getToken();
        SendTokenToServer(refreshedToken);
        new PreferenceHelper(this).saveFcmToken(refreshedToken);

    }

    public void SendTokenToServer(String token) {
        Log.d("Token","Fcm Token generated ==>"+token);
    //    Toast.makeText(this, token, Toast.LENGTH_SHORT).show();
    }
}
