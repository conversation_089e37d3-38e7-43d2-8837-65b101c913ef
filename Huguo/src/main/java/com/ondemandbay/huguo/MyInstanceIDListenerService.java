package com.ondemandbay.huguo;

import android.app.Service;
import android.widget.Toast;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import java.util.Map;

/**
 * Created by Abid on 5/2/2018.
 */

public class MyInstanceIDListenerService extends FirebaseMessagingService {

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        Map data = remoteMessage.getData();
        String title = data.get("title").toString(); //your key value instead of 'title'
        String message = data.get("message").toString(); //your key value instead of 'de
        sendNotification(title, message);
    }

    public void sendNotification(String head, String Content){
        Toast.makeText(this, head +" " + Content, Toast.LENGTH_SHORT).show();
    }
}
