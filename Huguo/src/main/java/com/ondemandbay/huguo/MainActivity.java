package com.ondemandbay.huguo;

import android.app.Activity;
import android.app.Dialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.Window;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import android.Manifest;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.crashlytics.android.Crashlytics;
import com.google.firebase.iid.FirebaseInstanceId;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.ondemandbay.huguo.R;

import com.ondemandbay.huguo.component.MyFontButton;
import com.ondemandbay.huguo.utils.AndyUtils;
import com.ondemandbay.huguo.utils.PreferenceHelper;
//import io.fabric.sdk.android.Fabric;

public class MainActivity extends Activity implements OnClickListener
{

    private static final int MY_PERMISSIONS_ACCESS_FINE_LOCATION = 1000;
    /**
     * Called when the activity is first created.
     */
    private MyFontButton btnSignIn, btnRegister;
    private ImageView taxibooking;
    private PreferenceHelper pHelper;
    private boolean isReceiverRegister;


//	private int oldOptions;
    private BroadcastReceiver mHandleMessageReceiver = new BroadcastReceiver()
{

        @Override
        public void onReceive(Context context, Intent intent) {
            AndyUtils.removeCustomProgressDialog();
            if (intent.getAction().equals(CommonUtilities.DISPLAY_REGISTER_GCM)) {
                Bundle bundle = intent.getExtras();
                if (bundle != null) {
                    int resultCode = bundle.getInt(CommonUtilities.RESULT);
                    if (resultCode == Activity.RESULT_OK) {
                        Toast.makeText(MainActivity.this,
                                "okk",
                                Toast.LENGTH_SHORT).show();

                    } else {
                        Toast.makeText(MainActivity.this,
                                getString(R.string.register_gcm_failed),
                                Toast.LENGTH_SHORT).show();
                        finish();
                    }
                }
            }
        }
    };

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //Fabric.with(this, new Crashlytics());
        // if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        // Window window = getWindow();
        // window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        // window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
        // window.setStatusBarColor(getColor(getActivity(),
        // R.color.color_action_bar_main));
        //
        // }
        setContentView(R.layout.activity_main);
        TextView textViewPolicy = (TextView) findViewById(R.id.textViewPrivacyPolicy);
        textViewPolicy.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent myIntent = new Intent(MainActivity.this, PrivacyPolicyActivity.class);
                MainActivity.this.startActivity(myIntent);
            }
        });

        if (checkPermissions()){
            continueLoading();
        }
        
    }

    void continueLoading(){

        if (!TextUtils.isEmpty(new PreferenceHelper(this).getUserId())) {
            startActivity(new Intent(this, MainDrawerActivity.class));
            this.finish();
            return;
        }
        isReceiverRegister = false;
        pHelper = new PreferenceHelper(this);

        // Intent startRegisterActivity = new Intent(MainActivity.this,RegisterActivity.class);
        //startRegisterActivity.putExtra("isSignin", true);
        //startActivity(startRegisterActivity);

        btnSignIn = (MyFontButton) findViewById(R.id.btnSignIn);
        btnRegister = (MyFontButton) findViewById(R.id.btnRegister);
        taxibooking = (ImageView) findViewById(R.id.booknowTaxi);
        btnSignIn.setOnClickListener(this);
        btnRegister.setOnClickListener(this);
        taxibooking.setOnClickListener(this);

        String refreshtoken = "";
       // if (TextUtils.isEmpty(pHelper.getFcmToken())&&TextUtils.isEmpty(pHelper.getDeviceToken())) {

            isReceiverRegister = true;
            refreshtoken = FirebaseInstanceId.getInstance().getToken();
            pHelper.putDeviceToken(refreshtoken);
            pHelper.saveFcmToken(refreshtoken);
            //   registerGcmReceiver(mHandleMessageReceiver);
      //  }
//        Log.d("device client token= ", refreshtoken);

    }

    private boolean checkPermissions() {
        if (ContextCompat.checkSelfPermission(this,
                Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            return true;
        } else {
            requestPermissions();
            return false;
        }
    }

    private void requestPermissions() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                        Manifest.permission.CAMERA,
                        Manifest.permission.READ_CONTACTS,
                        Manifest.permission.CALL_PHONE,
                        Manifest.permission.READ_EXTERNAL_STORAGE},
                MY_PERMISSIONS_ACCESS_FINE_LOCATION);
    }


    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String permissions[], @NonNull int[] grantResults) {

        switch (requestCode) {
            case MY_PERMISSIONS_ACCESS_FINE_LOCATION:
                continueLoading();
                break;

        }
    }

    public void registerGcmReceiver(BroadcastReceiver mHandleMessageReceiver) {
        if (mHandleMessageReceiver != null) {
            AndyUtils.showCustomProgressDialog(this,
                    "Loading", false, null);
            new GCMRegisterHendler(this, mHandleMessageReceiver);

        }
    }

    public void unregisterGcmReceiver(BroadcastReceiver mHandleMessageReceiver) {
        if (mHandleMessageReceiver != null) {
            if (mHandleMessageReceiver != null) {
                unregisterReceiver(mHandleMessageReceiver);
            }
        }
    }

    @Override
    public void onClick(View v)
    {
       // showtoast("Yes="+v.getId());
        Intent startRegisterActivity = new Intent(MainActivity.this,
                RegisterActivity.class);
        switch (v.getId()) {
            case R.id.btnSignIn:
                //showtoast("1");
                startRegisterActivity.putExtra("isSignin", true);
                break;
            case R.id.btnRegister:
               // showtoast("2");
                startRegisterActivity.putExtra("isSignin", false);
                break;
            case R.id.booknowTaxi:
                 //showtoast("3");
                startRegisterActivity.putExtra("isSignin", false);
                break;
        }
        startActivity(startRegisterActivity);
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
        finish();
        //*/
    }

    @Override
    public void onDestroy() {

        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        // super.onBackPressed();
        openExitDialog();
    }

    public void openExitDialog() {
        final Dialog mDialog = new Dialog(this);
        mDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);

        mDialog.getWindow().setBackgroundDrawable(
                new ColorDrawable(android.graphics.Color.TRANSPARENT));
        mDialog.setContentView(R.layout.exit_layout);
        mDialog.setCancelable(false);
        mDialog.findViewById(R.id.tvExitOk).setOnClickListener(
                new View.OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        mDialog.dismiss();
                        finish();
                        overridePendingTransition(R.anim.slide_in_left,
                                R.anim.slide_out_right);

                    }
                });
        mDialog.findViewById(R.id.tvExitCancel).setOnClickListener(
                new View.OnClickListener() {

                    @Override
                    public void onClick(View v) {
                        mDialog.dismiss();
                    }
                });
        mDialog.show();
    }

    //((( Show Toast message ))))
    public void showtoast(String str)
    {
        Toast.makeText(this,str,Toast.LENGTH_LONG).show();
    }
}