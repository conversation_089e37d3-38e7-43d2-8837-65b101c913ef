package com.ondemandbay.huguo.firebase;

import java.util.Map;

public class DataObject {
    String type;
    String title;
    String body;

    public static DataObject parse(Map<String, String> dataObject) {
        DataObject object = new DataObject();
        object.type = dataObject.get("type");
        object.title = dataObject.get("title");
        object.body = dataObject.get("body");
        return object;
    }

    public int getType() {
        try {
            return Integer.parseInt(type);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public String getTitle() {
        return title;
    }

    public String getBody() {
        return body;
    }
}
