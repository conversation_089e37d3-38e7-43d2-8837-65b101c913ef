package com.ondemandbay.huguo.firebase;

import android.app.Activity;
import android.content.Intent;
import android.util.Log;

import com.google.firebase.iid.FirebaseInstanceId;
import com.google.firebase.iid.FirebaseInstanceIdService;


public class MyFirebaseInstanceIDService extends FirebaseInstanceIdService {

    private static final String TAG = "MyFirebaseIIDService";

    @Override
    public void onTokenRefresh() {
        //Getting registration token
        String refreshedToken = FirebaseInstanceId.getInstance().getToken();
        Log.d(TAG, "Refreshed token: " + refreshedToken);

        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // Instance ID token to your app server.
        sendRegistrationToServer(refreshedToken);
    }

    public  void sendRegistrationToServer(String token) {
        publishResults(token, Activity.RESULT_OK);
    }

    public void publishResults(String regid, int result) {
      /*  AndyUtils.removeSimpleProgressDialog();
        Intent intent = new Intent(CommonUtilities.DISPLAY_MESSAGE_ACTION);
        intent.putExtra(CommonUtilities.RESULT, result);
        intent.putExtra(CommonUtilities.REGID, regid);
        // System.out.println("sending broad cast");
        sendBroadcast(intent);*/
    }
}


