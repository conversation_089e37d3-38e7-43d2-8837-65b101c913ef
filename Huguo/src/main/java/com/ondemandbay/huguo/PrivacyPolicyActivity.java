package com.ondemandbay.huguo;

import android.os.Bundle;

import android.view.View;
import android.webkit.WebView;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;



public class PrivacyPolicyActivity extends AppCompatActivity {


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_privacy_policy);

//        getSupportActionBar().hide();


        WebView myWebView = (WebView) findViewById(R.id.webview);
        myWebView.loadUrl(getString(R.string.privacy_policy_url));

        Button fermerButton = (Button) findViewById(R.id.button_fermer);
        fermerButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                finish();
            }
        });


    }

}