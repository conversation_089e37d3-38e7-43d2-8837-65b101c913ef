package com.ondemandbay.huguo.utils;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Location;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.ActivityCompat;
import androidx.fragment.app.DialogFragment;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GooglePlayServicesUtil;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.GoogleApiClient.ConnectionCallbacks;
import com.google.android.gms.common.api.GoogleApiClient.OnConnectionFailedListener;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationListener;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.Priority;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.tasks.OnSuccessListener;

/**
 * <AUTHOR> elluminati.in
 */
public class LocationHelper implements LocationListener,
        OnConnectionFailedListener, ConnectionCallbacks {
    MediaPlayer player;
    private String TAG = "LocationHelper";
    private FusedLocationProviderClient fusedLocationClient;
    private LocationCallback locationCallback;
    private LocationRequest locationRequest;

    public interface OnLocationReceived {
        public void onLocationReceived(LatLng latlong);

        public void onLocationReceived(Location location);

        public void onConntected(Bundle bundle);

        public void onConntected(Location location);
    }

    private LocationRequest mLocationRequest;
    private GoogleApiClient mGoogleApiClient;
    //	private static final int REQUEST_CODE = 2;
//	private static final int REQUEST_RESOLVE_ERROR = 1001;
    // Bool to track whether the app is already resolving an error
    private boolean mResolvingError = false;
    private OnLocationReceived mLocationReceived;

    private Context context;
    public final String APPTAG = "LocationSample";

    private LatLng latLong;

    private final int INTERVAL = 5000;
    private final int FAST_INTERVAL = 1000;
    public final static int CONNECTION_FAILURE_RESOLUTION_REQUEST = 9000;
    private boolean isLocationReceived = false;

    public LocationHelper(Context context) {
        this.context = context;
        createLocationRequest();
        buildGoogleApiClient();
        if (!mResolvingError) { // more about this later
            mGoogleApiClient.connect();
        }
        initLocationRequest();
    }

    private void initLocationRequest() {
        Log.i(TAG, "initLocationRequest");
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(context);

        locationRequest = new LocationRequest.Builder(Priority.PRIORITY_BALANCED_POWER_ACCURACY, 5000)
                .setWaitForAccurateLocation(false)
                .setMinUpdateIntervalMillis(2000)
                .setMaxUpdateDelayMillis(100)
                .build();

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            return;
        }
        fusedLocationClient.getLastLocation()
                .addOnSuccessListener((Activity) context, new OnSuccessListener<Location>() {
                    @Override
                    public void onSuccess(Location location) {
                        // Got last known location. In some rare situations this can be null.
                        if (location != null) {
                          //  Log.i(TAG, "locationCallback: location: " + location.toString());
                            onLocationChanged(location);
                        }
                    }
                });

        startLocationUpdates();
    }



    private void startLocationUpdates() {
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            //    ActivityCompat#requestPermissions
            // here to request the missing permissions, and then overriding
            //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
            //                                          int[] grantResults)
            // to handle the case where the user grants the permission. See the documentation
            // for ActivityCompat#requestPermissions for more details.
            return;
        }



        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(LocationResult locationResult) {
                if (locationResult == null) {
                    return;
                }
                //Log.i(TAG, "locationCallback: location: " + locationResult.getLastLocation().toString());
                onLocationChanged(locationResult.getLastLocation());


            }
        };

        fusedLocationClient.requestLocationUpdates(locationRequest,
                locationCallback,
                Looper.getMainLooper());
    }

    private void stopLocationUpdates() {
        fusedLocationClient.removeLocationUpdates(locationCallback);
    }

    public void setLocationReceivedLister(OnLocationReceived mLocationReceived) {
        this.mLocationReceived = mLocationReceived;
    }

    public LatLng getCurrentLocation() {
        return latLong;
    }

//	public LatLng getLastLatLng() {
//		// If Google Play Services is available
//		if (servicesConnected()) {
//			Location location = null;
//			if (mGoogleApiClient.isConnected()) {
//				location = LocationServices.FusedLocationApi
//						.getLastLocation(mGoogleApiClient);
//
//			}
//
//			// Get the current location
//
//			// Display the current location in the UI
//			latLong = getLatLng(location);
//		}
//
//		return latLong;
//	}

//	public Location getLastLocation() {
//		// If Google Play Services is available
//		if (servicesConnected()) {
//			Location location = null;
//			if (mGoogleApiClient.isConnected()) {
//				location = LocationServices.FusedLocationApi
//						.getLastLocation(mGoogleApiClient);
//
//			}
//
//			// Get the current location
//
//			// Display the current location in the UI
//			latLong = getLatLng(location);
//		}
//		Location location = new Location("");
//		if (latLong != null) {
//			location.setLatitude(latLong.latitude);
//			location.setLongitude(latLong.longitude);
//			return location;
//		}
//		return null;
//	}

    public void onStart() {
//		if (mGoogleApiClient != null && !mGoogleApiClient.isConnected()) {
//			mGoogleApiClient.connect();
//		} else {
//			startPeriodicUpdates();
////			player = MediaPlayer.create(context,
////					Settings.System.DEFAULT_RINGTONE_URI);
////			player.setLooping(true);
////			player.start();
//		}
        startLocationUpdates();
    }

    public void onResume() {
//		if (mGoogleApiClient.isConnected()) {
//			startPeriodicUpdates();
//		}

    }

    public void onPause() {
        if (mGoogleApiClient.isConnected()) {
            stopPeriodicUpdates();

        }
    }

    public void onStop() {
        // If the client is connected
//		if (mGoogleApiClient.isConnected()) {
//			stopPeriodicUpdates();
//
//		}

        // After disconnect() is called, the client is considered "dead".
        //mGoogleApiClient.disconnect();
        stopLocationUpdates();
    }

    private boolean servicesConnected() {

        // Check that Google Play services is available
        int resultCode = GooglePlayServicesUtil
                .isGooglePlayServicesAvailable(context);

        // If Google Play services is available
        if (ConnectionResult.SUCCESS == resultCode) {
            // Continue
            return true;
            // Google Play services was not available for some reason
        } else {
            // Display an error dialog
            // Dialog dialog = GooglePlayServicesUtil.getErrorDialog(resultCode,
            // context, 0);
            // if (dialog != null) {
            // ErrorDialogFragment errorFragment = new ErrorDialogFragment();
            // errorFragment.setDialog(dialog);
            // errorFragment.show(context.getSupportFragmentManager(), APPTAG);
            //
            // }
            return false;
        }
    }

    @Override
    public void onLocationChanged(Location location) {
        //Log.i("LocationHelper", "onLocationChanged = " + location.toString());

        if (!isLocationReceived && mLocationReceived != null) {
            mLocationReceived.onConntected(location);
            isLocationReceived = true;
        }

        if (mLocationReceived != null) {
            mLocationReceived.onLocationReceived(location);
        }
        latLong = getLatLng(location);

        if (mLocationReceived != null && latLong != null) {
            mLocationReceived.onLocationReceived(latLong);
        }

    }

    @Override
    public void onConnectionFailed(ConnectionResult connectionResult) {
        if (mResolvingError) {
            // Already attempting to resolve an error.
            return;
        }
        // else if (connectionResult.hasResolution()) {
        // try {
        // mResolvingError = true;
        // connectionResult.startResolutionForResult(context,
        // CONNECTION_FAILURE_RESOLUTION_REQUEST);
        //
        // } catch (SendIntentException e) {
        // // There was an error with the resolution intent. Try again.
        // mGoogleApiClient.connect();
        // }
        // } else {
        // // Show dialog using GooglePlayServicesUtil.getErrorDialog()
        // // showErrorDialog(arg0.getErrorCode());
        //
        // showErrorDialog(connectionResult.getErrorCode());
        // mResolvingError = true;
        // }

    }

    @Override
    public void onConnected(Bundle connectionHint) {
        startPeriodicUpdates();
        if (mLocationReceived != null)
            mLocationReceived.onConntected(connectionHint);
    }

    private void startPeriodicUpdates() {
//
//		Location location = LocationServices.FusedLocationApi
//				.getLastLocation(mGoogleApiClient);
//		if (location != null) {
//			onLocationChanged(location);
//		}
//		LocationServices.FusedLocationApi.requestLocationUpdates(
//				mGoogleApiClient, mLocationRequest, this);

    }

    /**
     * In response to a request to stop updates, send a request to Location
     * Services
     */
    private void stopPeriodicUpdates() {
        LocationServices.FusedLocationApi.removeLocationUpdates(
                mGoogleApiClient, this);

    }

    // public void showErrorDialog(int errorCode) {
    //
    // // Get the error dialog from Google Play services
    // Dialog errorDialog = GooglePlayServicesUtil.getErrorDialog(errorCode,
    // context, CONNECTION_FAILURE_RESOLUTION_REQUEST);
    //
    // // If Google Play services can provide an error dialog
    // if (errorDialog != null) {
    //
    // // Create a new DialogFragment in which to show the error dialog
    // ErrorDialogFragment errorFragment = new ErrorDialogFragment();
    //
    // // Set the dialog in the DialogFragment
    // errorFragment.setDialog(errorDialog);
    //
    // // Show the error dialog in the DialogFragment
    // errorFragment.show(context.getSupportFragmentManager(), APPTAG);
    // }
    // }

    public static class ErrorDialogFragment extends DialogFragment {

        // Global field to contain the error dialog
        private Dialog mDialog;

        /**
         * Default constructor. Sets the dialog field to null
         */
        public ErrorDialogFragment() {
            super();
            mDialog = null;
        }

        /**
         * Set the dialog to display
         *
         * @param dialog
         *            An error dialog
         */
        public void setDialog(Dialog dialog) {
            mDialog = dialog;
        }

        /*
         * This method must return a Dialog to the DialogFragment.
         */
        @Override
        public Dialog onCreateDialog(Bundle savedInstanceState) {
            return mDialog;
        }
    }

    protected void createLocationRequest() {
        mLocationRequest = new LocationRequest();
        mLocationRequest.setInterval(INTERVAL);
        mLocationRequest.setFastestInterval(FAST_INTERVAL);
        mLocationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
    }

    protected synchronized void buildGoogleApiClient() {

        mGoogleApiClient = new GoogleApiClient.Builder(context)
                .addConnectionCallbacks(this)
                .addOnConnectionFailedListener(this)
                .addApi(LocationServices.API).build();

    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.google.android.gms.common.api.GoogleApiClient.ConnectionCallbacks
     * #onConnectionSuspended(int)
     */
    @Override
    public void onConnectionSuspended(int arg0) {

    }

    public LatLng getLatLng(Location currentLocation) {
        // If the location is valid
        if (currentLocation != null) {
            // Return the latitude and longitude as strings
            LatLng latLong = new LatLng(currentLocation.getLatitude(),
                    currentLocation.getLongitude());

            return latLong;
        } else {
            // Otherwise, return the empty string
            return null;
        }
    }
}