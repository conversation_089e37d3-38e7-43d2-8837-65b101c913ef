package com.ondemandbay.huguo.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.androidquery.AQuery;
import com.androidquery.callback.ImageOptions;
import com.ondemandbay.huguo.R;
import com.ondemandbay.huguo.models.ApplicationPages;

import java.util.ArrayList;

/**
 * <AUTHOR> ellum<PERSON>ti.in
 */
public class DrawerAdapter extends BaseAdapter {
    ArrayList<ApplicationPages> listMenu;
    AQuery aQuery;
    //	private int images[] = { R.drawable.nav_profile, R.drawable.nav_payment,
//			R.drawable.nav_support, R.drawable.nav_share };
//	private String items[];
    private ViewHolder holder;
    private LayoutInflater inflater;
    private ImageOptions imageOptions;

    public DrawerAdapter(Context context, ArrayList<ApplicationPages> listMenu) {
        this.listMenu = listMenu;
        inflater = (LayoutInflater) context
                .getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        aQuery = new AQuery(context);
        imageOptions = new ImageOptions();
        imageOptions.fileCache = true;
        imageOptions.memCache = true;
        imageOptions.fallback = R.drawable.ic_launcher;
    }

    @Override
    public int getCount() {
        return listMenu.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.drawer_item, parent, false);
            holder = new ViewHolder();
            holder.tvMenuItem = (TextView) convertView
                    .findViewById(R.id.tvMenuItem);
//			holder.ivMenuImage = (ImageView) convertView
//					.findViewById(R.id.ivMenuImage);

            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
//		if (position == 0) {
//			aQuery.id(holder.ivMenuImage).image(R.drawable.nav_profile);
//		} else if (position == 1) {
//			aQuery.id(holder.ivMenuImage).image(R.drawable.nav_payment);
//		} else if (position == 2) {
//			aQuery.id(holder.ivMenuImage).image(R.drawable.ub__nav_history);
//		} else if (position == 3) {
//			aQuery.id(holder.ivMenuImage).image(R.drawable.nav_referral);
//		} else if (position == (listMenu.size() - 1)) {
//			aQuery.id(holder.ivMenuImage).image(R.drawable.ub__nav_logout);
//		} else {
//			if (TextUtils.isEmpty(listMenu.get(position).getIcon())) {
//				aQuery.id(holder.ivMenuImage).image(R.drawable.ic_launcher);
//			} else {
//				aQuery.id(holder.ivMenuImage).image(
//						listMenu.get(position).getIcon());
//			}
//
//		}
        holder.tvMenuItem.setText(listMenu.get(position).getTitle());
        Log.e("ho","fdf"+listMenu.get(position).getTitle());
//        if(holder.tvMenuItem.getText().toString().equalsIgnoreCase("My Bookings"))
//        {
//            holder.tvMenuItem.setVisibility(View.GONE);
//        }
        return convertView;
    }

    class ViewHolder {
        public TextView tvMenuItem;
//		public ImageView ivMenuImage;
    }

}
