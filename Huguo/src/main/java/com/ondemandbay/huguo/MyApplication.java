package com.ondemandbay.huguo;

import android.content.Context;

import androidx.multidex.MultiDexApplication;

import org.acra.ACRA;
import org.acra.BuildConfig;
import org.acra.config.CoreConfigurationBuilder;
import org.acra.config.MailSenderConfiguration;
import org.acra.config.MailSenderConfigurationBuilder;
import org.acra.data.StringFormat;

public class MyApplication extends MultiDexApplication {
    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        ACRA.DEV_LOGGING = true;


        ACRA.init(this, new CoreConfigurationBuilder()
                //core configuration:
                .withBuildConfigClass(BuildConfig.class)
                .withReportFormat(StringFormat.JSON)
                .withPluginConfigurations(
                        new MailSenderConfigurationBuilder()
                                //required
                                .withMailTo("<EMAIL>")
                                //defaults to true
                                .withReportAsFile(true)
                                //defaults to ACRA-report.stacktrace
                                .withReportFileName("Crash-HuguoClientAndroid.json")
                                //defaults to "<applicationId> Crash Report"
                                .withSubject("Crash from HuguoClientAndroid-R1.7")
                                //defaults to empty
                                .withBody("")
                                .build()
                )

        );
    }
}
