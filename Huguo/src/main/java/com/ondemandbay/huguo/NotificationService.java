package com.ondemandbay.huguo;

import android.app.Dialog;
import android.app.IntentService;
import android.app.Notification;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.service.notification.StatusBarNotification;
import androidx.annotation.Nullable;
import android.util.Log;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.ondemandbay.huguo.R;

import com.ondemandbay.huguo.parse.ParseContent;
import com.ondemandbay.huguo.utils.Const;
import com.ondemandbay.huguo.utils.PreferenceHelper;

import java.util.Timer;
import java.util.TimerTask;

/**
 * Created by pankaj on 12/14/2017.
 */

public class NotificationService extends IntentService {
    protected PreferenceHelper preferenceHelper;
    RequestQueue requestQueue;
    ParseContent parseContent;
    private boolean isApprovedCheck = true;
    public Dialog mDialog;
    Timer timer;
    @Override
    public void onCreate() {
        super.onCreate();
        parseContent= new ParseContent(this);
        preferenceHelper = new PreferenceHelper(this);
        requestQueue= Volley.newRequestQueue(this);
         timer=new Timer();
        timer.scheduleAtFixedRate(new Timertask(),0,5000);

//        ClientRequestFragment clientRequestFragment= new ClientRequestFragment();
//        clientRequestFragment.getAllRequests();

    }

    public NotificationService() {
        super("notification");
    }



    @Override
    protected void onHandleIntent(@Nullable Intent intent) {
        timer=new Timer();
        timer.scheduleAtFixedRate(new Timertask(),0,5000);
    }


    private void sendRequest() {
        StringRequest stringRequest = new StringRequest(Request.Method.GET, Const.ServiceType.GET_REQUEST_STATUS + Const.Params.ID + "="
                + new PreferenceHelper(this).getUserId() + "&"
                + Const.Params.TOKEN + "="
                + new PreferenceHelper(this).getSessionToken()
                + "&" + Const.Params.REQUEST_ID + "=" + String
                .valueOf(new PreferenceHelper(this).getRequestId()),
                new Response.Listener<String>() {
                    @Override
                    public void onResponse(String response) {
                        Log.d("RESPONSE>>>>", response);

                        switch (parseContent.checkRequestStatus(response)) {
                            case Const.IS_WALKER_STARTED:
                                sendNotification();
                                break;
                        }


                    }
                }, new Response.ErrorListener() {
            @Override
            public void onErrorResponse(VolleyError error) {

            }

        });
        requestQueue.add(stringRequest);
    }


    public class Timertask extends TimerTask
    {

        @Override
        public void run() {
            sendRequest();
        }
    }

    private void sendNotification(){
            timer.cancel();
        Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);
            Intent intent= new Intent(this,MainActivity.class);
        PendingIntent pendingIntent=PendingIntent.getActivity(this,101,intent,PendingIntent.FLAG_IMMUTABLE );
        Notification notification=new Notification.Builder(this)
                                    .setContentTitle("Trip Accepted")
                                    .setContentText("Your Trip Is Accepted By Driver.")
                                    .setContentIntent(pendingIntent)
                                    .setSmallIcon(R.drawable.ic_launcher)
                                    .setSound(alarmSound)
                                    .setAutoCancel(false)
                                    .build();

            NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            if(!isvisiblenotification()) {
                notificationManager.notify(101, notification);


            }   
    }


   private boolean isvisiblenotification()

   {
       NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);

       StatusBarNotification[] notifications = notificationManager.getActiveNotifications();
       for (StatusBarNotification notifi : notifications) {
           if (notifi.getId() == 101) {
               return true;
           }
       }
       return false;
    }

}
