package com.ondemandbay.huguo.parse;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.AsyncTask;
import android.util.Log;
import android.widget.Toast;

import com.ondemandbay.huguo.utils.AndyUtils;
import com.ondemandbay.huguo.utils.AppLog;
import com.ondemandbay.huguo.utils.Const;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;

/**
 * <AUTHOR> elluminati.in
 */
public class HttpRequester {
    private String TAG = "HttpRequester";
    private Map<String, String> map;
    private AsyncTaskCompleteListener mAsynclistener;
    private int serviceCode;
    private boolean isGetMethod = false;
    private HttpClient httpclient;
    private Activity activity;
    private AsyncHttpRequest request;

    public HttpRequester(Activity activity, Map<String, String> map,
                         int serviceCode, AsyncTaskCompleteListener asyncTaskCompleteListener) {
        this.map = map;
        this.serviceCode = serviceCode;
        this.activity = activity;
        this.isGetMethod = false;
        // is Internet Connection Available...




        if (AndyUtils.isNetworkAvailable(activity)) {
            mAsynclistener = (AsyncTaskCompleteListener) asyncTaskCompleteListener;
            // request = (AsyncHttpRequest) new AsyncHttpRequest().execute(map
            // .get("url"));
            Log.i(TAG, map.toString());
            request = (AsyncHttpRequest) new AsyncHttpRequest()
                    .executeOnExecutor(Executors.newSingleThreadExecutor(),
                            map.get("url"));
        } else {
            showToast("Network is not available!!!");
        }
    }

    public HttpRequester(Activity activity, Map<String, String> map,
                         int serviceCode, boolean isGetMethod,
                         AsyncTaskCompleteListener asyncTaskCompleteListener) {
        this.map = map;
        this.serviceCode = serviceCode;
        this.isGetMethod = isGetMethod;
        this.activity = activity;
        this.mAsynclistener = asyncTaskCompleteListener;

        // is Internet Connection Available...

        //Log.i(TAG, "URL = " + map.get("url"));

        if (AndyUtils.isNetworkAvailable(activity)) {
            mAsynclistener = (AsyncTaskCompleteListener) asyncTaskCompleteListener;
            // request = (AsyncHttpRequest) new AsyncHttpRequest().execute(map
            // .get("url"));
            request = (AsyncHttpRequest) new AsyncHttpRequest()
                    .executeOnExecutor(Executors.newSingleThreadExecutor(),
                            map.get("url"));
        } else {
            showToast("Network is not available!!!");
        }
    }

    private void showToast(String msg) {
        Toast.makeText(activity, msg, Toast.LENGTH_SHORT).show();
    }

    public void cancelTask() {

        request.cancel(true);
        // System.out.println("task is canelled");

    }

    class AsyncHttpRequest extends AsyncTask<String, Void, String> {

        @Override
        protected String doInBackground(String... urls) {
            map.remove("url");
            try {
                Thread.currentThread().setPriority(Thread.MAX_PRIORITY);
                //Log.i(TAG, "isGetMethod: " + isGetMethod);
                if (!isGetMethod) {
                    HttpPost httppost = new HttpPost(urls[0]);
                    httpclient = new DefaultHttpClient();

                    HttpConnectionParams.setConnectionTimeout(
                            httpclient.getParams(), 30000);

                    List<NameValuePair> nameValuePairs = new ArrayList<NameValuePair>();

                    for (String key : map.keySet()) {
//                        AppLog.Log(Const.TAG,
//                                key + "  < === >  " + map.get(key));

                        nameValuePairs.add(new BasicNameValuePair(key, map
                                .get(key)));
                    }

                    httppost.setEntity(new UrlEncodedFormEntity(nameValuePairs));
                    ActivityManager manager = (ActivityManager) activity
                            .getSystemService(Context.ACTIVITY_SERVICE);

                    if (manager.getMemoryClass() < 25) {
                        System.gc();
                    }

                 //   Log.i(TAG, "httppost: " + httppost.getEntity().toString());



                    HttpResponse response = httpclient.execute(httppost);

                    String responseBody = EntityUtils.toString(response
                            .getEntity());

                   // Log.i(TAG, "responseBody: " + responseBody);

                    return responseBody;

                } else {
                    httpclient = new DefaultHttpClient();

                    // System.out.println("GET URL " + urls[0]);
                    HttpConnectionParams.setConnectionTimeout(
                            httpclient.getParams(), 30000);
                    HttpGet httpGet = new HttpGet(urls[0]);

                    HttpResponse httpResponse = httpclient.execute(httpGet);
                    HttpEntity httpEntity = httpResponse.getEntity();

                    String responseBody = EntityUtils.toString(httpEntity);

                    //Log.i(TAG, "responseBody: " + responseBody);

                    return responseBody;
                }
            } catch (Exception e) {
                e.printStackTrace();
            } catch (OutOfMemoryError oume) {
                System.gc();

                Toast.makeText(
                        activity.getParent().getParent(),
                        "Run out of memory please colse the other background apps and try again!",
                        Toast.LENGTH_LONG).show();
            } finally {
                if (httpclient != null)
                    httpclient.getConnectionManager().shutdown();

            }

            return null;

        }

        @Override
        protected void onPostExecute(String response) {

        //    Log.i(TAG, "onPostExecute - " + response);

            if (mAsynclistener != null) {
            //    Log.i(TAG, "mAsynclistener != NULL");
                mAsynclistener.onTaskCompleted(response, serviceCode);
            }else{
               // Log.i(TAG, "mAsynclistener == NULL");
            }

        }
    }
}
