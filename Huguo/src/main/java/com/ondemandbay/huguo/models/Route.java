package com.ondemandbay.huguo.models;

import java.util.ArrayList;

/**
 * <AUTHOR> elluminati.in
 */
public class Route {

    private String startAddress;
    private String endAddress;
    private double startLat;
    private double startLon;
    private double endLat;
    private double endLon;
    private String distanceText;
    private int distanceValue;
    private String durationText;
    private int durationValue;
    private ArrayList<Step> listStep;

    public Route() {
        listStep = new ArrayList<Step>();
    }

    public ArrayList<Step> getListStep() {
        return listStep;
    }

    public void setListStep(ArrayList<Step> listStep) {
        this.listStep = listStep;
    }

    public String getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(String startAddress) {
        this.startAddress = startAddress;
    }

    public String getEndAddress() {
        return endAddress;
    }

    public void setEndAddress(String endAddress) {
        this.endAddress = endAddress;
    }

    public double getStartLat() {
        return startLat;
    }

    public void setStartLat(double startLat) {
        this.startLat = startLat;
    }

    public double getStartLon() {
        return startLon;
    }

    public void setStartLon(double startLon) {
        this.startLon = startLon;
    }

    public double getEndLat() {
        return endLat;
    }

    public void setEndLat(double endLat) {
        this.endLat = endLat;
    }

    public double getEndLon() {
        return endLon;
    }

    public void setEndLon(double endLon) {
        this.endLon = endLon;
    }

    public String getDistanceText() {
        return distanceText;
    }

    public void setDistanceText(String distanceText) {
        this.distanceText = distanceText;
    }

    public int getDistanceValue() {
        return distanceValue;
    }

    public void setDistanceValue(int distanceValue) {
        this.distanceValue = distanceValue;
    }

    public String getDurationText() {
        return durationText;
    }

    public void setDurationText(String durationText) {
        this.durationText = durationText;
    }

    public int getDurationValue() {
        return durationValue;
    }

    public void setDurationValue(int durationValue) {
        this.durationValue = durationValue;
    }

}
