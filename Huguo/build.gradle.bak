apply plugin: 'com.android.application'

android {
    compileSdkVersion 27
    // Read version information from local file and increment as appropriate
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.canRead()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        def versionMajor = versionProps['VERSION_MAJOR'].toInteger()
        def versionMinor = versionProps['VERSION_MINOR'].toInteger()
        def versionBuild = versionProps['VERSION_BUILD'].toInteger() + 1
        // Update the build number in the local file
        versionProps['VERSION_BUILD'] = versionBuild.toString()
        versionProps.store(versionPropsFile.newWriter(), null)
        defaultConfig {
            versionCode versionBuild
            versionName "${versionMajor}.${versionMinor}." + String.format("%05d", versionBuild)
        }
    }

    buildToolsVersion '27.0.3'


    defaultConfig {
        applicationId "com.ondemandbay.huguo"
        minSdkVersion 15
        targetSdkVersion 27
        multiDexEnabled true
        setProperty("archivesBaseName", "HuguoClient-$versionName")

    }

    signingConfigs {
        release {
            storeFile file('C:/HuguoClient-R20181002/buildsystem/release.keystore')
            storePassword "m34gj9b4r3"
            keyAlias 'key0'
            keyPassword "m34gj9b4r3"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            aaptOptions.setProperty("cruncherEnabled", false)

            signingConfig signingConfigs.release
        }
    }
    dexOptions {
        javaMaxHeapSize "2g" //specify the heap size for the dex process
        preDexLibraries = false //delete the already predexed libraries

    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
    }
   /* lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }*/
}

dependencies {
    implementation 'com.stripe:stripe-android:2.0.2'
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.android.support:multidex:1.0.0'
    implementation 'com.android.support:appcompat-v7:27.1.1'
    //compile 'com.stripe:stripe-android:+'
    implementation 'com.soundcloud.android:android-crop:1.0.1@aar'
    implementation 'de.hdodenhof:circleimageview:2.0.0'
    implementation 'com.sromku:simple-fb:4.1.1'
    implementation files('libs/android-query.0.26.7.jar')
    implementation files('libs/apache-mime4j-core-0.7.2.jar')
    implementation files('libs/httpclient-4.3.5.jar')
    implementation files('libs/httpcore-4.3.2.jar')
    implementation files('libs/httpmime-4.3.5.jar')
    implementation files('libs/splunk-mint-4.0.8.jar')
    // implementation files('libs/gcm.jar')
    implementation project(':library_pinnlistivew')
    implementation project(':fancylibrary')
    implementation 'me.neavo:volley:2014.12.09'
    ///add volley notwork class
    implementation 'com.google.firebase:firebase-messaging:11.8.0'
    implementation 'com.google.android.gms:play-services-maps:11.8.0'
    implementation 'com.google.android.gms:play-services-plus:11.8.0'
    implementation 'com.google.android.gms:play-services-location:11.8.0'
    implementation 'com.squareup.picasso:picasso:2.71828'
}

apply plugin: 'com.google.gms.google-services'
