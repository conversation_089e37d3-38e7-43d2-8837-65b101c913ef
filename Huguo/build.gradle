buildscript {
    repositories {
        maven { url 'https://maven.fabric.io/public' }
    }

    dependencies {
       // classpath 'io.fabric.tools:gradle:1.+'
    }

}
apply plugin: 'com.android.application'
//apply plugin: 'io.fabric'

repositories {
    maven { url 'https://maven.fabric.io/public' }
}


android {
        namespace "com.ondemandbay.huguo"
        signingConfigs {
        config {
            keyAlias 'key0'
            keyPassword 'm34gj9b4r3'
            storeFile file('/Users/<USER>/Desktop/Huguo/HuguoClientAndroid-R1.6/buildsystem/release.keystore')
            storePassword 'm34gj9b4r3'
        }
    }

    // Read version information from local file and increment as appropriate
    def versionPropsFile = file('version.properties')
    if (versionPropsFile.canRead()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        def versionMajor = versionProps['VERSION_MAJOR'].toInteger()
        def versionMinor = versionProps['VERSION_MINOR'].toInteger()
        def versionBuild = versionProps['VERSION_BUILD'].toInteger() + 1
        // Update the build number in the local file
        versionProps['VERSION_BUILD'] = versionBuild.toString()
        versionProps.store(versionPropsFile.newWriter(), null)
        defaultConfig {
            versionCode versionBuild
            versionName "${versionMajor}.${versionMinor}." + String.format("%05d", versionBuild)
        }
    }
    buildToolsVersion '34.0.0'
    defaultConfig {
        applicationId "com.ondemandbay.huguo"
        minSdkVersion 26
        targetSdkVersion 35
        compileSdkVersion 35
        compileSdk 35
        multiDexEnabled true
        setProperty("archivesBaseName", "HuguoClient-$versionName")
    }
//    applicationVariants.all { variant ->
//        variant.outputs.all {
//            outputFileName = "../../../../../../publish/" + outputFileName
//        }
//    }
    lintOptions {
        abortOnError false
    }
    /*signingConfigs {
       *//* release {
            storeFile file("C:\\Users\\<USER>\\StudioProjects\\HuguoClient\\buildsystem\\release.keystore")
            storePassword "m34gj9b4r3"
            keyAlias 'key0'
            keyPassword "m34gj9b4r3"
        }*//*
    }*/
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            aaptOptions.setProperty("cruncherEnabled", false)
            debuggable false
            signingConfig signingConfigs.config
        }
    }
//    dexOptions {
//        javaMaxHeapSize "2g" //specify the heap size for the dex process
//        preDexLibraries = false //delete the already predexed libraries
//
//    }
    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
            exclude 'META-INF/androidx.localbroadcastmanager_localbroadcastmanager.version'
            exclude 'META-INF/androidx.swiperefreshlayout_swiperefreshlayout.version'
            exclude 'META-INF/androidx.print_print.version'
            exclude 'META-INF/androidx.customview_customview.version'
            exclude 'META-INF/androidx.cursoradapter_cursoradapter.version'
            exclude 'META-INF/androidx.drawerlayout_drawerlayout.version'
            exclude 'META-INF/androidx.versionedparcelable_versionedparcelable.version'
            exclude 'META-INF/androidx.interpolator_interpolator.version'
            exclude 'META-INF/androidx.fragment_fragment.version'
            exclude 'META-INF/androidx.vectordrawable_vectordrawable.version'
            exclude 'META-INF/androidx.core_core.version'
            exclude 'META-INF/androidx.legacy_legacy-support-core-ui.version'
            exclude 'META-INF/androidx.legacy_legacy-support-core-utils.version'
            exclude 'META-INF/proguard/androidx-annotations.pro'
            exclude 'META-INF/androidx.slidingpanelayout_slidingpanelayout.version'
            exclude 'META-INF/androidx.print_print.version'
            exclude 'META-INF/androidx.documentfile_documentfile.version'
            exclude 'META-INF/androidx.versionedparcelable_versionedparcelable.version'
            exclude 'META-INF/androidx.asynclayoutinflater_asynclayoutinflater.version'
            exclude 'META-INF/androidx.drawerlayout_drawerlayout.version'
            exclude 'META-INF/androidx.interpolator_interpolator.version'
            exclude 'META-INF/androidx.appcompat_appcompat.version'
            exclude 'META-INF/androidx.swiperefreshlayout_swiperefreshlayout.version'
            exclude 'META-INF/androidx.loader_loader.version'
            exclude 'META-INF/androidx.viewpager_viewpager.version'
            exclude 'META-INF/androidx.coordinatorlayout_coordinatorlayout.version'
            exclude 'META-INF/androidx.cursoradapter_cursoradapter.version'
        exclude 'META-INF/androidx.vectordrawable_vectordrawable-animated.version'


        }

}
def acraVersion = '5.11.3'

dependencies {
    implementation 'com.stripe:stripe-android:20.48.6'
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.android.support:appcompat-v7:28.0.0'
    //compile 'com.stripe:stripe-android:+'
    implementation 'com.soundcloud.android:android-crop:1.0.1@aar'
    implementation 'de.hdodenhof:circleimageview:3.1.0'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'
    implementation 'androidx.annotation:annotation:1.0.2'
    implementation("com.google.firebase:firebase-iid:21.1.0")

    // implementation 'com.sromku:simple-fb:4.1.1'
    implementation files('libs/android-query.0.26.7.jar')
    implementation files('libs/apache-mime4j-core-0.7.2.jar')
    implementation files('libs/httpclient-4.3.5.jar')
    implementation files('libs/httpcore-4.3.2.jar')
    implementation files('libs/httpmime-4.3.5.jar')
    implementation files('libs/splunk-mint-4.0.8.jar')
    // implementation files('libs/gcm.jar')
    implementation project(':library_pinnlistivew')
    implementation "androidx.viewpager:viewpager:1.0.0"
    implementation project(':fancylibrary')
    implementation 'me.neavo:volley:2014.12.09'
    ///add volley notwork class
    implementation 'com.google.firebase:firebase-messaging:24.0.1'
    implementation 'com.google.firebase:firebase-core:16.0.0'

    implementation 'com.google.android.gms:play-services-maps:19.0.0'
    implementation 'com.google.android.gms:play-services-plus:17.0.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'


    implementation 'com.squareup.picasso:picasso:2.71828'
    implementation 'com.makeramen:roundedimageview:2.3.0'
    implementation 'com.crashlytics.sdk.android:crashlytics:2.10.1'


    implementation "ch.acra:acra-dialog:$acraVersion"
    implementation "ch.acra:acra-mail:$acraVersion"



}

apply plugin: 'com.google.gms.google-services'
