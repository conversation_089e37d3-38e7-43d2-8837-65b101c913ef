<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2013 <PERSON>
  ~           2013 <PERSON>
  ~
  ~    Licensed under the Apache License, Version 2.0 (the "License");
  ~    you may not use this file except in compliance with the License.
  ~    You may obtain a copy of the License at
  ~
  ~        http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~    Unless required by applicable law or agreed to in writing, software
  ~    distributed under the License is distributed on an "AS IS" BASIS,
  ~    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~    See the License for the specific language governing permissions and
  ~    limitations under the License.
  -->
<resources>
    <declare-styleable name="FancyCoverFlow">
        <attr name="unselectedAlpha" format="float"/>
        <attr name="unselectedSaturation" format="float"/>
        <attr name="unselectedScale" format="float"/>
        <attr name="maxRotation" format="integer"/>
        <attr name="scaleDownGravity" format="float"/>
        <attr name="actionDistance" format="integer">
            <enum name="auto" value="**********" />
        </attr>
    </declare-styleable>
</resources>
